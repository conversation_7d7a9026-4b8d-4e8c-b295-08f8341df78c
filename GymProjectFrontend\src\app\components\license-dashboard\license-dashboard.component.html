<!-- license-dashboard.component.html -->
<div class="container mt-4">
    <h2>Owner Dashboard - Salon Yönetim Paneli</h2>

    <div *ngIf="isLoading" class="d-flex justify-content-center my-5">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <div *ngIf="!isLoading" class="dashboard-content">
      <!-- Statistics Cards -->
      <div class="row mt-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Toplam Admin</h5>
                  <h2 class="mt-2 mb-0">{{ totalAdmins }}</h2>
                  <small>Salon Sahipleri</small>
                </div>
                <i class="fas fa-users fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3">
          <div class="card bg-success text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Aktif Lisanslar</h5>
                  <h2 class="mt-2 mb-0">{{ totalActiveLicenses }}</h2>
                  <small>Çalışan Salonlar</small>
                </div>
                <i class="fas fa-certificate fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3">
          <div class="card bg-warning text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Kritik Uyarılar</h5>
                  <h2 class="mt-2 mb-0">{{ expiringLicenses + expiredLicenses }}</h2>
                  <small>Müdahale Gerekli</small>
                </div>
                <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-3">
          <div class="card bg-info text-white mb-4">
            <div class="card-body">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <h5 class="card-title mb-0">Bu Ay Gelir</h5>
                  <h2 class="mt-2 mb-0">{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h2>
                  <small>Lisans Ödemeleri</small>
                </div>
                <i class="fas fa-money-bill-wave fa-3x opacity-50"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Admin (Salon Sahipleri) Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h4>Salon Sahipleri (Admin) Bilgileri</h4>
            </div>
            <div class="card-body">
              <div *ngIf="allAdmins.length === 0" class="alert alert-info">
                Henüz kayıtlı salon sahibi bulunmamaktadır.
              </div>

              <div *ngIf="allAdmins.length > 0" class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Salon Sahibi</th>
                      <th>Salon Adı</th>
                      <th>Şehir</th>
                      <th>İlçe</th>
                      <th>Telefon</th>
                      <th>Email</th>
                      <th>Adres</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let admin of allAdmins">
                      <td>{{ admin.companyUserName }}</td>
                      <td>{{ admin.companyName }}</td>
                      <td>{{ admin.cityName }}</td>
                      <td>{{ admin.townName }}</td>
                      <td>{{ admin.companyUserPhoneNumber }}</td>
                      <td>{{ admin.companyUserEmail }}</td>
                      <td>{{ admin.companyAdress }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Kritik Uyarılar ve Gelir Bilgileri -->
      <div class="row mt-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header bg-warning text-white">
              <h4>⚠️ Kritik Uyarılar</h4>
            </div>
            <div class="card-body">
              <!-- Süresi Dolan Lisanslar -->
              <div *ngIf="hasExpiredLicenses" class="alert alert-danger">
                <h6><i class="fas fa-times-circle"></i> Süresi Dolan Lisanslar ({{ expiredLicenses }})</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <tbody>
                      <tr *ngFor="let license of expiredLicensesList">
                        <td>{{ license.userName }}</td>
                        <td>{{ license.companyName }}</td>
                        <td class="text-danger">{{ license.remainingDays }} gün</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Yakında Sona Erecek Lisanslar -->
              <div *ngIf="hasExpiringLicenses" class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Yakında Sona Erecek ({{ expiringLicenses }})</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <tbody>
                      <tr *ngFor="let license of expiringLicensesList">
                        <td>{{ license.userName }}</td>
                        <td>{{ license.companyName }}</td>
                        <td class="text-warning">{{ license.remainingDays }} gün</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div *ngIf="!hasExpiringLicenses && !hasExpiredLicenses" class="alert alert-success">
                <i class="fas fa-check-circle"></i> Tüm lisanslar aktif durumda!
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="card">
            <div class="card-header bg-success text-white">
              <h4>💰 Gelir Bilgileri</h4>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-6">
                  <div class="text-center p-3 bg-light rounded">
                    <h5>Bu Ay</h5>
                    <h3 class="text-success">{{ monthlyRevenue | currency:'TRY':'symbol':'1.0-0' }}</h3>
                  </div>
                </div>
                <div class="col-6">
                  <div class="text-center p-3 bg-light rounded">
                    <h5>Toplam</h5>
                    <h3 class="text-primary">{{ totalRevenue | currency:'TRY':'symbol':'1.0-0' }}</h3>
                  </div>
                </div>
              </div>

              <hr>
              <h6>Son 5 İşlem</h6>
              <div *ngIf="recentTransactions.length === 0" class="alert alert-info">
                İşlem bulunmamaktadır.
              </div>

              <div *ngIf="recentTransactions.length > 0" class="table-responsive">
                <table class="table table-sm">
                  <tbody>
                    <tr *ngFor="let transaction of recentTransactions">
                      <td>{{ transaction.transactionDate | date:'dd/MM' }}</td>
                      <td>{{ transaction.amount | currency:'TRY':'symbol':'1.0-0' }}</td>
                      <td><small>{{ transaction.paymentMethod }}</small></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- Owner Hızlı İşlemler -->
      <div class="row mt-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header bg-dark text-white">
              <h4>🚀 Owner Hızlı İşlemler</h4>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-3">
                  <a routerLink="/company-user-management" class="btn btn-primary btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Admin Yönetimi</span>
                    <i class="fas fa-users-cog"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/license-packages" class="btn btn-success btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Lisans Paketleri</span>
                    <i class="fas fa-certificate"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/user-licenses" class="btn btn-warning btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Tüm Lisanslar</span>
                    <i class="fas fa-list"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/license-transactions" class="btn btn-info btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Gelir Raporları</span>
                    <i class="fas fa-chart-line"></i>
                  </a>
                </div>
              </div>

              <div class="row">
                <div class="col-md-3 mb-3">
                  <a routerLink="/expired-licenses" class="btn btn-danger btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Kritik Durumlar</span>
                    <i class="fas fa-exclamation-triangle"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/system-settings" class="btn btn-secondary btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Sistem Ayarları</span>
                    <i class="fas fa-cogs"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <a routerLink="/reports" class="btn btn-dark btn-block d-flex align-items-center justify-content-between p-3">
                    <span>Detaylı Raporlar</span>
                    <i class="fas fa-file-alt"></i>
                  </a>
                </div>
                <div class="col-md-3 mb-3">
                  <button class="btn btn-outline-primary btn-block d-flex align-items-center justify-content-between p-3" (click)="loadDashboardData()">
                    <span>Verileri Yenile</span>
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>